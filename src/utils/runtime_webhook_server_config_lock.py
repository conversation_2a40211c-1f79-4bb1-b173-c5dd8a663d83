# 使用该代码实现跨进程webhook server运行时相同配置项的唯一性锁,如果对应配置项key1已经存在value1,那么其他进程就不能对key1也设置value1,直到对应进程生命周期结束

import logging
import sqlite3
import threading
import time
from typing import Dict
from zoneinfo import ZoneInfo

from config import constants
from models.base_config_unique_manage import BaseConfigUniquenessManager
from utils import server_utils

# 配置日志
logger = logging.getLogger(__name__)
# 多进程webhook server运行时配置项唯一性管理器（单例实现）
class RuntimeWebhookConfigManager(BaseConfigUniquenessManager):
    """
    多进程配置项唯一性管理器（单例模式）
    注意事项：
    由于写入数据库中的时间字段的时区是UTC,在使用SQL时也是UTC,但SELECT返回的是UTC时区,可能不是我所需要的,这时就需要手动转换时区,使用SQLiteBaseManager.convert_utc_str()函数
    """

    HEARTBEAT_TIMEOUT = 300  # 5分钟心跳超时 [s]
    HEARTBEAT_INTERVAL = 30  # 30秒心跳间隔 [s]

    def __init__(self, db_path: str = constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging: bool = True, zone: ZoneInfo = None):
        logger.info(f"initializing runtime webhook server config uniqueness manager with db_path: {db_path}, sql_logging: {enable_sql_logging}")
        super().__init__(db_path=db_path,enable_sql_logging= enable_sql_logging,heartbeat_interval=RuntimeWebhookConfigManager.HEARTBEAT_INTERVAL,heartbeat_timeout=RuntimeWebhookConfigManager.HEARTBEAT_TIMEOUT,zone=zone)
        logger.info("sqlite base manager initialized")

        self._registered_items: Dict[str, str] = {}  # 当前进程注册的配置项 {key: value} 为了注册时键值的唯一性检测和避免空元素的空心跳
        self._items_lock = threading.Lock() #_registered_items访问的并发锁,使用时注意不能嵌套,嵌套使用会导致死锁
    def _init_db(self):
        """初始化数据库表结构"""
        def init_tables(cur: sqlite3.Cursor):
            # 创建配置注册表
            init_sql="""
            CREATE TABLE IF NOT EXISTS multi_process_config_registry (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key TEXT NOT NULL,   -- 配置键
                config_value TEXT NOT NULL, -- 配置值
                pid INTEGER NOT NULL,       -- 进程ID
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 优化唯一索引：
            CREATE UNIQUE INDEX IF NOT EXISTS idx_key_value ON multi_process_config_registry (config_key, config_value);
            
            -- 优化键索引
            CREATE INDEX IF NOT EXISTS idx_config_key  ON multi_process_config_registry (config_key);
                
            -- 进程ID索引
            CREATE INDEX IF NOT EXISTS idx_pid ON multi_process_config_registry (pid);
                
            -- 心跳时间戳索引  
            CREATE INDEX IF NOT EXISTS idx_last_heartbeat ON multi_process_config_registry (last_heartbeat);                         
            """
            cur.executescript(init_sql)

        self._exec_retry(init_tables)
        logger.info("cross process config lock database tables initialized")
    def _start_heartbeat_thread(self):
        """启动心跳线程（仅当有注册项时运行）"""
        with self._items_lock:
            if not self._registered_items:
                return
            super()._start_heartbeat_thread()

    def _heartbeat_worker(self):
        """心跳工作线程"""
        while not self._stop_heartbeat_event.is_set():
            try:
                with self._items_lock:
                    if not self._registered_items:
                        logger.info("no registered items, stopping heartbeat")
                        break

                self.update_heartbeats()
                with self._items_lock:
                     self._heartbeat_failure_count = 0  # 重置失败计数器
                # 使用可中断的等待 每30秒发送一次心跳
                if self._stop_heartbeat_event.wait(timeout=self.HEARTBEAT_INTERVAL):
                    break
            except Exception as e:
                with self._items_lock:
                    self._heartbeat_failure_count += 1
                    cur_failure_count = self._heartbeat_failure_count
                logger.error(f"heartbeat thread error: {e}")
                # 连续失败多次后重启线程
                if cur_failure_count >= 3:
                    logger.error("heartbeat failed 3 times, attempting to restart thread")
                    self._restart_heartbeat_thread()
                    return
                if not self._stop_heartbeat_event.is_set():
                    time.sleep(5)  # 出错后等待5秒再重试

        logger.info("webhook heartbeat worker stopped")

    def _cleanup_inactive_records(self, cur: sqlite3.Cursor):
        """清理非活动记录"""
        # 清理超过心跳超时时间的记录
        cur.execute("""
        DELETE FROM multi_process_config_registry 
        WHERE last_heartbeat < datetime('now', ?)
        """, (f"-{self.HEARTBEAT_TIMEOUT} seconds",))
        deleted_timeout = cur.rowcount
        # 清理已终止进程的记录
        try:
            # 获取所有数据库中的PID
            cur.execute("SELECT DISTINCT pid FROM multi_process_config_registry")
            db_pids = {row[0] for row in cur.fetchall()}

            # 获取系统所有活动PID
            active_pids = server_utils.get_active_pids()

            # 找出已终止的PID
            dead_pids = db_pids - active_pids

            # 清理已死进程的记录
            if dead_pids:
                placeholders = ",".join("?" for _ in dead_pids)
                cur.execute(f"""
                DELETE FROM multi_process_config_registry 
                WHERE pid IN ({placeholders})
                """, tuple(dead_pids))

                deleted_dead = cur.rowcount
            else:
                deleted_dead = 0
        except Exception as e:
            logger.warning(f"error during process cleanup: {e}")
            deleted_dead = 0

            # 记录清理结果
        if deleted_timeout > 0 or deleted_dead > 0:
            logger.info(f"Cleaned up {deleted_timeout} timeout and {deleted_dead} dead process records")
        elif deleted_timeout == 0 and deleted_dead == 0:
            logger.debug("No inactive records to clean up")

    def register_config(self, config_key: str, config_value: str) -> bool:
        logger.info(f"attempting to register config: {config_key}={config_value}")

        # 添加参数验证
        if not config_key or not isinstance(config_key, str):
            raise ValueError("config_key must be a non-empty string")

        if not config_value or not isinstance(config_value, str):
            raise ValueError("config_value must be a non-empty string")

        logger.info("parameter validation passed")

        # 先检查本地缓存，避免不必要的数据库操作
        with self._items_lock:
            if config_key in self._registered_items:
                logger.error(f"config key {config_key} already registered with value {self._registered_items[config_key]} in this process")
                return False

        logger.info("local cache check passed")
        def _register(cur: sqlite3.Cursor):
            # 清理非活动记录
            self._cleanup_inactive_records(cur)
            # 注册新配置项 --- key-value唯一
            cur.execute("""
            INSERT  OR IGNORE  INTO multi_process_config_registry (config_key, config_value, pid) VALUES (?, ?, ?)
            """, (config_key, config_value, self._pid))
            if cur.rowcount == 0:
                logger.warning(f"config key-value pair ({config_key}={config_value}) already exists")
                return False
            # 添加到当前进程的注册字典
            with self._items_lock:
                self._registered_items[config_key] = config_value
            self._start_heartbeat_thread()  # 确保心跳线程运行

            logger.info(f"registered config: {config_key}={config_value} for pid {self._pid}")
            return True

        try:
            success = self._exec_retry(_register)
            if not success:
                logger.error(f"failed to register config {config_key}={config_value}")
            return success
        except Exception as e:
            logger.error(f"error registering config {config_key}={config_value}!\n{e}")
            return False
    def unregister_config(self, config_key: str, config_value: str) -> bool:
        # 先检查本地缓存
        with self._items_lock:
            if config_key not in self._registered_items:
                logger.warning(f"Config {config_key} not registered in this process")
                return False
            if self._registered_items[config_key] != config_value:
                logger.error(f"unregister config's value[{config_key}-{config_value}] not exist in this process,can not unregister")
                return False
        def _unregister(cursor: sqlite3.Cursor):
            cursor.execute("""
            DELETE FROM multi_process_config_registry 
            WHERE config_key = ? AND config_value = ? AND pid = ?
            """, (config_key, config_value, self._pid))
            if cursor.rowcount !=1:
                logger.error(f"unregister config: {config_key}={config_value} failed")
                return False
            with self._items_lock:
                if config_key in self._registered_items and self._registered_items[config_key] == config_value:
                    del self._registered_items[config_key]

                # 如果没有注册项，停止心跳
                if not self._registered_items:
                    self._stop_heartbeat_thread()
            return True

        try:
            return self._exec_retry(_unregister)
        except Exception:# noqa
            logger.exception(f"error unregistering config {config_key}={config_value}!")
            return False
    def update_heartbeats(self):
        """更新当前进程所有配置项的心跳时间"""
        with self._items_lock:
            if not self._registered_items:
                return
            items = list(self._registered_items.items())
        def _update(cur: sqlite3.Cursor):
            # 批量更新（使用单个SQL语句）
            params = []
            for config_key, config_value in items:
                params.extend([config_key, config_value, self._pid])

            # 构建批量更新SQL
            placeholders = ",".join(["(?, ?, ?)"] * len(items))
            sql = f"""
            UPDATE multi_process_config_registry 
            SET last_heartbeat = CURRENT_TIMESTAMP 
            WHERE (config_key, config_value, pid) IN ({placeholders})
            """

            cur.execute(sql, params)

            updated_count = cur.rowcount
            if updated_count != len(items):
                logger.warning(f"updated {updated_count} heartbeats, expected {len(items)}")

        self._exec_retry(_update)
        logger.debug(f"updated heartbeats for {len(self._registered_items)} configs")
    def _cleanup_current_process(self):
        """清理当前进程的所有配置项"""
        with self._items_lock:
            if not self._registered_items:
                server_utils.logger_print("no registered items to clean up", logger, log_level=logging.DEBUG)
                return
            items = list(self._registered_items.items())

        def _cleanup(cur: sqlite3.Cursor):
            # 构建批量删除SQL
            params = []
            for config_key, config_value in items:
                params.extend([config_key, config_value, self._pid])

            placeholders = ",".join(["(?, ?, ?)"] * len(items))
            sql = f"""
            DELETE FROM multi_process_config_registry 
            WHERE (config_key, config_value, pid) IN ({placeholders})
            """

            cur.execute(sql, params)

            deleted_count = cur.rowcount
            server_utils.logger_print(f"cleaned up {deleted_count}/{len(items)} configs for PID {self._pid}", logger, log_level=logging.INFO)

            # 确保本地状态被清除
            with self._items_lock:
                for config_key, config_value in items:
                    if config_key in self._registered_items and self._registered_items[config_key] == config_value:
                        del self._registered_items[config_key]

        try:
            self._exec_retry(_cleanup)
        except Exception as e:
            server_utils.logger_print("error cleaning up config registry", logger, log_level=logging.ERROR,use_exception=True,print_error=True,exception=e)
        finally:
            with self._items_lock:
                self._registered_items.clear() # 清空注册字典
            self._stop_heartbeat_thread()  # 停止心跳线程

    def get_active_configs(self) -> Dict[str, Dict[str, int]]:
        """获取当前所有活动的配置项"""
        def _query(cur: sqlite3.Cursor):
            cur.execute("""
            SELECT config_key, config_value, pid 
            FROM multi_process_config_registry 
            WHERE last_heartbeat > datetime('now', ?)
            """, (f"-{self.HEARTBEAT_TIMEOUT} seconds",))

            # 组织为字典结构: {key: {value: pid}}
            result: Dict[str, Dict[str,int]] = {}
            for row_config_key, row_config_value, pid in cur.fetchall():
                result.setdefault(row_config_key, {})[row_config_value] = pid
            return result

        try:
            return self._exec_retry(_query)
        except Exception: # noqa
            logger.exception("error getting active configs!")
            return {}

    def get_valid_values_for_key(self, config_key: str) -> Dict[str, int]:
        """获取指定键下所有活动的值及其进程ID"""
        def _query(cur: sqlite3.Cursor):
            cur.execute("""
            SELECT config_value, pid 
            FROM multi_process_config_registry 
            WHERE config_key = ? 
            AND last_heartbeat > datetime('now', ?)
            """, (config_key, f"-{self.HEARTBEAT_TIMEOUT} seconds"))
            return {config_value: pid for config_value, pid in cur.fetchall()}

        try:
            return self._exec_retry(_query)
        except Exception: # noqa
            logger.exception(f"error getting active values for key {config_key}!")
            return {}

    def check_config_registrable(self, config_key: str, config_value: str) -> bool:
        def _check(cur: sqlite3.Cursor):
            cur.execute("""
            SELECT COUNT(*) 
            FROM multi_process_config_registry 
            WHERE config_key = ? AND config_value = ?
            AND last_heartbeat > datetime('now', ?)
            """, (config_key, config_value, f"-{self.HEARTBEAT_TIMEOUT} seconds"))
            return cur.fetchone()[0] == 0

        try:
            return self._exec_retry(_check)
        except Exception :# noqa
            logger.exception("error checking config availability!")
            return False
