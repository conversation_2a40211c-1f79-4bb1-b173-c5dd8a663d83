# 包含了与当前服务端相关的静态函数
import asyncio
import configparser
import hashlib
import inspect
import logging
import os
import socket
import sys
import threading
import time
import traceback
from datetime import datetime, timedelta
from ipaddress import ip_address, ip_network
from typing import Callable, Any, Tuple, Dict, Optional, Literal
from typing import List, Awaitable, Union, Set
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

import chardet
import psutil

from config import constants, gui_constants
from utils import self_log

logger = logging.getLogger(__name__)
# 全局注册表，保存每个 func 对应的执行状态和锁
_run_once_locks: dict[Callable[..., Any], threading.Lock] = {}
_run_once_flags: dict[Callable[..., Any], bool] = {}
_run_once_global_lock = threading.Lock()

# 获取大于start_time时间的符合target_time的 datetime 对象（包含年月日时分秒）;过期的不要
def get_next_datetime(start_time: datetime, target_time: datetime.time) -> datetime:
    # 获取和启动时间相同的date的 target_time 时间
    date_start_time = start_time.date()
    date_target = datetime.combine(date_start_time, target_time, tzinfo=start_time.tzinfo)
    if start_time < date_target:
        return date_target

    # 构造明天的 target_time 时间
    tomorrow_date = date_start_time + timedelta(days=1)
    return datetime.combine(tomorrow_date, target_time, tzinfo=start_time.tzinfo)


# 判断IP是否在白名单中
def is_ip_in_whitelist(client_ip: str, whitelist: list[str]) -> bool:
    logger.info(f"checking if ip {client_ip} is in whitelist: {whitelist}")
    try:
        parsed_client_ip = ip_address(client_ip)
        logger.info(f"parsed client ip: {parsed_client_ip}, version: {parsed_client_ip.version}")

        for i, whitelist_ip in enumerate(whitelist):
            logger.info(f"checking against whitelist entry {i+1}: {whitelist_ip}")
            network = ip_network(whitelist_ip, strict=False)
            logger.info(f"parsed network: {network}, version: {network.version}")

            if parsed_client_ip.version == network.version and parsed_client_ip in network:
                logger.info(f"ip {client_ip} matches whitelist entry {whitelist_ip}")
                return True

        logger.info(f"ip {client_ip} does not match any whitelist entries")
        return False
    except ValueError:
        logger.exception(f"invalid ip address: {client_ip}!")
        return False


# 转换存储单位为字节数,storage_str 如 "1.25MB"
def convert_storage_str_to_bytes(storage_str: str) -> float:
    storage_str = trim(storage_str)
    if storage_str is None:
        raise ValueError("storage string can not be empty!")
    storage_str = storage_str.replace(" ", "")
    str_match = constants.STORAGE_UNIT_PATTERN.match(storage_str)
    if not str_match:
        raise ValueError(f"invalid storage string: {storage_str}")
    num, unit = str_match.groups()
    num = float(num)
    unit = unit.upper()
    try:
        multiplier = constants.STORAGE_UNIT[unit]
    except KeyError:
        raise ValueError(f"unknown unit: {unit!r} in {storage_str!r}")

    return float(num * multiplier)


# 该函数用于保证函数只执行一次且线程安全[使用该函数越多,对应的func也需要锁和状态描述,造成内存会越来越大:绑定在类实例上]
def run_once(func: Callable[..., Any], *args, **kwargs) -> Any:
    func_name = getattr(func, '__name__', str(func))
    logger.info(f"run_once called for function: {func_name}")

    with _run_once_global_lock:
        if func not in _run_once_locks:
            logger.info(f"initializing lock and flag for function: {func_name}")
            _run_once_locks[func] = threading.Lock()
            _run_once_flags[func] = False
        else:
            logger.info(f"using existing lock for function: {func_name}")

    lock = _run_once_locks[func]
    with lock:
        if not _run_once_flags[func]:
            logger.info(f"executing function {func_name} for the first time")
            _run_once_flags[func] = True
            result = func(*args, **kwargs)
            logger.info(f"function {func_name} executed successfully")
            return result
        else:
            logger.info(f"function {func_name} already executed, skipping")


# 该函数用于将字符串中前后空格去除,为空则返回None
def trim(str_value: str | None) -> str | None:
    if not isinstance(str_value, str):
        logger.warning(f"current value is not string, current value is {str_value}, type is {type(str_value)}")
        return None
    strip_value = str_value.strip()
    return strip_value if strip_value else None


# 配置项key对应的值必须可以转换成整数
def tran_int(key: str, value: str) -> int:
    try:
        return int(value)
    except ValueError as e:
        raise ValueError(f"config key:{key} - value:{value} must be a valid integer!") from e

# 该函数用于将配置文件中对应section节点内容并转换为字典
def section_to_dict(config: configparser.ConfigParser, section_name: str, allow_value_none: bool = False,allow_empty_section: bool = False) -> Dict[
    str, Any]:
    if section_name is None or not config.has_section(section_name):
        raise ValueError(f"current section :{section_name} not exist in config file!")
    config_file_section = config[section_name]
    res = {}
    for raw_key, raw_value in config_file_section.items():
        key = trim(raw_key)
        if key is None:
            raise ValueError(f"{section_name} section key can not be empty,current key is {raw_key}")
        if key in res:
            raise ValueError(f"{section_name} section key can not be duplicated,current key is {raw_key}")
        value = trim(raw_value)
        if value is None and not allow_value_none:
            raise ValueError(
                f"{section_name} section value can not be empty,current key is {raw_key},current value is {raw_value}")
        res[key] = value
    if not res and not allow_empty_section:
        raise ValueError(f"{section_name} section can not be empty!")
    return res

# 获取本地文件路径实际对应的全路径:如果文件不存在则抛出异常
def get_real_exist_file_path(path: str) -> str:
    try:
        real_path = os.path.realpath(os.path.expanduser(path))
        if not os.path.isfile(real_path):
            raise ValueError(f"file not exist: {path}")
        return real_path
    except OSError as e:
        raise ValueError(f"invalid file path: {path}") from e

# 获取对应路径的真实路径,并创建目录:[如果path是目录,则创建目录 --- path_is_dir=True]
def get_real_path_create_dir(path: str,path_is_dir:bool=False) -> str:
    real_path = os.path.realpath(os.path.expanduser(path))
    # make sure the directory exists
    os.makedirs(os.path.dirname(real_path), exist_ok=True)
    if path_is_dir:
        os.makedirs(real_path, exist_ok=True)
    return real_path

def is_utf8_encoding_file(file_path, chunk_size=4096):
    """
    判断指定文件路径对应文件是否是UTF-8编码的文件
    前提:
    1. 文件必须存在
    2. 文件必须可读
    """
    file_path=os.path.realpath(os.path.expanduser(file_path))
    if not os.path.isfile(file_path) or not os.access(file_path, os.R_OK):
        return False
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(chunk_size)
            if not chunk:
                # 空文件被视为合法的UTF-8编码文件
                return True
            file_info=chardet.detect(chunk)
            encoding=file_info.get('encoding','').lower()
            confidence=file_info.get('confidence',0.0)
            logger.debug(f"文件{file_path}的编码为{encoding},置信度为{confidence}")
            return encoding in ['utf-8','utf-8-sig','ascii'] and confidence>0.9
    except UnicodeDecodeError:
        return False
    except Exception: # noqa
        logger.exception("检查编码时出错!")
        return False

def file_hash(file_path: str,
        algorithm: Literal[
            "md5", "sha1", "sha224", "sha256", "sha384", "sha512",
            "sha3_224", "sha3_256", "sha3_384", "sha3_512",
            "blake2b", "blake2s"
        ] = "sha256",
        chunk_size: int = 8192
) -> str:
    """
    计算文件的哈希值。

    Args:
        file_path: 要读取并计算哈希值的文件路径。
        algorithm: 哈希算法名称，默认 'sha256'。可选 'md5', 'sha1',
                   'sha224', 'sha384', 'sha512', 'sha3_224', 'sha3_256',
                   'sha3_384', 'sha3_512', 'blake2b', 'blake2s' 等。
        chunk_size: 每次读取的字节数，默认 8192。

    Returns:
        文件的十六进制哈希字符串。
    """
    if chunk_size <= 0:
        raise ValueError("chunk_size must be greater than 0!")
    if not os.path.isfile(file_path):
        raise ValueError(f"文件{file_path}实际不存在,无法计算哈希值!")
    # 创建对应算法的哈希对象
    try:
        hasher = hashlib.new(algorithm)
    except ValueError:
        raise ValueError(f"不支持的哈希算法: {algorithm}")

    # 分块读取文件并更新哈希
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hasher.update(chunk)
            return hasher.hexdigest()
    except OSError:
        raise ValueError(f"文件{file_path}无法读取,无法计算哈希值!")

# 检查输入的单个发信设备信息是否合法
def check_one_client_info(client_key: str|None, client_desc: str|None) -> bool:
    if not isinstance(client_key, str) or not isinstance(client_desc, str):
        return False
    return (constants.MIN_CLIENT_KEY_LEN <= len(
        client_key) <= constants.MAX_CLIENT_KEY_LEN) and constants.MIN_CLIENT_DESC_LEN < len(
        client_desc) and constants.CLIENT_KEY_PATTERN.match(client_key)


# 检查所有发信设备标识配置信息是否合法
def check_all_client_info_config(client_info_config: dict):
    for client_key, client_desc in client_info_config.items():
        if not check_one_client_info(client_key, client_desc):
            raise ValueError(constants.ERROR_CLIENT_INPUT_MSG)

def check_server_api_key(api_key: str) -> bool:
    return constants.SERVER_API_KEY_PATTERN.match(api_key)

# 该函数用于将字符串转换为布尔值,如果字符串为空则返回None
def str_to_bool(s: str) -> bool | None:
    s = trim(s)
    if s is None:
        return None
    if s.lower() == 'true':
        return True
    elif s.lower() == 'false':
        return False
    else:
        raise ValueError(f"invalid bool string: {s},the string must be 'true' or 'false'")

def check_time_zone_str(time_zone_str: str):
    try:
        ZoneInfo(time_zone_str)
    except ZoneInfoNotFoundError:
        raise ValueError(
            f"server properties [server]-[time_zone] value must be a valid timezone,current value is {time_zone_str}")

def check_lan_host(lan_host: str):
    # 必须是局域网IP
    try:
        host_ip = ip_address(lan_host)
    except ValueError:
        raise ValueError("server properties [server]-[host] value must be a valid IP address!")
    if not host_ip.is_private:
        raise ValueError("server properties [server]-[host] value must be a private IP address!")


# 该函数用于获取服务端配置的端口号
def get_server_config_port(port_str: str) -> int:
    port_int = tran_int("port", port_str)
    if not (constants.MIN_PORT <= port_int <= constants.MAX_PORT):
        raise ValueError(
            f"server properties [server]-[port] value must be between {constants.MIN_PORT} and {constants.MAX_PORT},current value is {port_str}")
    return port_int

# 将IP白名单对应的字符串转换为IP白名单列表:其中*表示允许所有IP访问
def get_whitelist(whitelist_config_value: str) -> List[str]:
    res_list:List[str] = []
    allowed_all = False
    for whitelist_ip in whitelist_config_value.split(","):
        # 校验字符串必须是IP或者网段
        new_whitelist_ip = trim(whitelist_ip)
        if new_whitelist_ip is None:
            continue
        # 不限制白名单
        if new_whitelist_ip == "*":
            allowed_all = True
            res_list.clear()
            res_list.append("*")
            continue
        # 校验IP或者网段是否合法
        try:
            ip_network(new_whitelist_ip, strict=False)
        except ValueError:
            raise ValueError(
                f"server properties [server]-[whitelist] value must be a valid IP or IP network,current value is {whitelist_ip}")
        if not allowed_all:
            res_list.append(new_whitelist_ip)
    if not res_list:
        raise ValueError("server properties [server]-[whitelist] value can not empty!")
    return res_list


def is_local_host(host: str)->bool:
    """判断host是否指代本机IP"""
    if not host or not isinstance(host, str):
        raise ValueError("host must be a non-empty string!")
    host=host.lower()
    if host in ["localhost", "127.0.0.1", "0.0.0.0","::1","0:0:0:0:0:0:0:1"]:
        return True
    try:
        host_ip = ip_address(host)
        return host_ip.is_loopback
    except ValueError:
        logger_print(f"invalid host: {host}",custom_logger=logger,log_level=logging.ERROR,use_exception=True)
        raise


def host_port_connect(
        host: str,port: int,
        timeout: float = 3.0,interval: float = 0.2,
        retries: int = 3,
        backoff_factor: float = 0.5
) -> bool:
    """
    端口连通性检测，支持重试和退避策略

    :param host: 目标主机IP或域名
    :param port: 目标端口
    :param timeout: 单次连接超时(秒)
    :param retries: 最大重试次数
    :param backoff_factor: 退避因子(指数退避基数)
    :param interval: 基础重试间隔(秒)
    :return: True 表示端口可连接，False 表示不可连接
    """
    if not (constants.MIN_PORT <= port <= constants.MAX_PORT):
        raise ValueError(f"port must be between {constants.MIN_PORT} and {constants.MAX_PORT},current value is {port}")
    logger.info(f"testing connection to {host}:{port} (timeout={timeout}s, retries={retries})")
    if host is None or not isinstance(host, str):
        raise ValueError("host must be a non-empty string!")
    if is_local_host(host):
        host = "127.0.0.1"  # 本地地址使用127.0.0.1代替

    for attempt in range(retries + 1):  # 包含首次尝试+重试次数
        try:
            # 设置临时socket避免TIME_WAIT状态影响
            with socket.create_connection((host, port), timeout=timeout) as sock:
                logger.info(f"Connection established to {host}:{port} (attempt {attempt+1})")
                # 立即关闭连接避免占用服务端资源
                sock.shutdown(socket.SHUT_RDWR)
                return True
        except (ConnectionRefusedError, socket.timeout) as e:
            # 特定异常时启用重试逻辑
            logger.warning(f"Attempt {attempt+1} failed: {type(e).__name__}: {e}")
            if attempt < retries:
                # 指数退避算法：interval * (backoff_factor ^ attempt)
                wait_time = interval * (backoff_factor ** attempt)
                logger.info(f"Retrying in {wait_time:.2f}s...")
                time.sleep(wait_time)
        except OSError as e:
            # 网络不可达等致命错误立即退出
            logger.error(f"Fatal error connecting to {host}:{port}: {e}")
            return False

    logger.error(f"All {retries+1} attempts failed for {host}:{port}")
    return False

def get_local_lan_ip(fallback: str = "127.0.0.1") -> str:
    """
    获取本机在局域网中的 IPv4 地址。

    原理：向一个外部地址（不一定可达）创建 UDP “连接”，
    OS 会自动选择一条出网的本地网卡和 IP，随后我们从 socket 获取到本地地址。

    :param fallback: 如果获取失败，返回的默认 IP（回环地址）。
    :return: 本机局域网 IPv4 地址字符串。
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 这里的 IP 和端口不需要真的可达，只要是合法的 IPv4 格式即可
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
    except Exception: # noqa
        local_ip = fallback
    return local_ip

TaskFuncType = Union[
    Callable[...,Any],
    Callable[..., Awaitable[Any]]
]

# 定时任务通用外部嵌套函数 --- 可以传递带有参数的同步/异步函数
async def run_safe_task(
        task_func: TaskFuncType,
        scheduler_check: Callable[[], bool],
        task_canceled_info: str,
        task_exception_info: str,
        custom_logger: Optional[logging.Logger] = None,
        *args,
        **kwargs
) -> None:
    try:
        if not scheduler_check():
            return

        if inspect.iscoroutinefunction(task_func):
            await task_func(*args, **kwargs)  # 异步函数
        else:
            task_func(*args, **kwargs)        # 同步函数
    except asyncio.CancelledError:
        logger_print(task_canceled_info,custom_logger=custom_logger)
    except BaseException as ex:
        logger_print(task_exception_info,custom_logger=custom_logger,use_exception=True,exception=ex)


# 打印日志信息 --- 在对于logger可能为空的情况下,兼容print打印日志信息
def logger_print(msg: str, custom_logger: logging.Logger,log_level:int=logging.INFO,use_exception:bool=False,print_error:bool=False, exception: Optional[BaseException] = None):
    show_msg = f"{msg}" if exception is None else f"{msg} \n {exception}"
    if not custom_logger or not self_log.log_config.had_init():
        print_tmp_file(msg,print_error=exception or print_error or log_level>=logging.WARNING, exception=exception)
        if exception:
            traceback.print_exc(file=sys.stderr)
    elif use_exception:
        custom_logger.exception(show_msg)
    else:
        custom_logger.log(log_level, show_msg)

def print_tmp_file(msg:str,print_error:bool=False, exception: Optional[BaseException] = None):
    """在没有logger的情况下,将日志信息打印到临时文件中"""
    show_msg = f"{msg}" if exception is None else f"{msg} \n {exception}"
    now=datetime.now(constants.DEFAULT_TIMEZONE)
    now_str=now.strftime(gui_constants.DATETIME_FORMAT)
    show_msg=f"{now_str} {show_msg}"
    tmp_file_path = constants.TEMP_LOG_FILE_PATH +now.strftime("%Y-%m-%d")+".log"
    tmp_log_file=get_real_path_create_dir(tmp_file_path,path_is_dir=False)
    with open(tmp_log_file,"a",encoding="utf-8") as f:
        f.write(show_msg)
        f.write("\n")
    if exception or print_error:
        print(show_msg, file=sys.stderr)
    else:
        print(show_msg)

def is_same_function(
    func1: Callable[..., Any],args1: Tuple[Any, ...],kwargs1: Dict[str, Any],
    func2: Callable[..., Any],args2: Tuple[Any, ...],kwargs2: Dict[str, Any],
) -> bool:
    """
    Compare two “function” (function + args/kwargs) for semantic equality.
    - Unwraps bound methods so that `obj.method` is tested by (function, instance).
    - Normalizes defaults via inspect.signature.bind_partial + apply_defaults.
    """

    def unwrap(f: Callable) -> Tuple[Callable, Optional[Any]]:
        """
        If f is a bound method, return (f.__func__, f.__self__),
        else (f, None).
        """
        # Unwrap decorator chain
        while hasattr(f, '__wrapped__'):
            f = f.__wrapped__
        if hasattr(f, "__self__") and hasattr(f, "__func__"):
            return f.__func__, f.__self__
        return f, None

    def normalize(
            f: Callable,
            args: Tuple[Any, ...],
            kwargs: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Bind given args/kwargs to the signature of f, fill in defaults,
        and return the full argument mapping.
        """
        sig = inspect.signature(f)
        try:
            bound = sig.bind(*args, **kwargs)  # 改为全绑定方式
        except TypeError:
            bound = sig.bind_partial(*args, **kwargs)
        bound.apply_defaults()
        # 处理可变参数
        arguments = dict(bound.arguments)
        for param in sig.parameters.values():
            if param.kind == param.VAR_POSITIONAL:
                arguments[param.name] = tuple(arguments.get(param.name, []))
            elif param.kind == param.VAR_KEYWORD:
                arguments[param.name] = dict(arguments.get(param.name, {}))

        return arguments

    # 1) Unwrap bound methods
    real_func1, self1 = unwrap(func1)
    real_func2, self2 = unwrap(func2)
    if real_func1 is not real_func2 or self1 != self2:
        return False

    # 2) Normalize argument binding
    try:
        norm1 = normalize(real_func1, args1, kwargs1)
        norm2 = normalize(real_func2, args2, kwargs2)
    except TypeError:
        # If they can’t even be bound, consider them unequal
        return False

    # 3) Compare final argument mappings
    return norm1 == norm2

def get_active_pids() -> Set[int]:
    """获取系统活动PID"""
    active_pids = set()
    for proc in psutil.process_iter(attrs=['status']):
        status = proc.info.get('status')  # type: ignore
        # 过滤僵尸进程
        if status and status != psutil.STATUS_ZOMBIE:
            active_pids.add(proc.pid)

    return active_pids

def update_config(file_path, section, config_dict):
    """
    更新配置文件中的指定节点内容

    参数:
        file_path: 配置文件路径
        section: 节点名称
        config_dict: 要更新/添加的配置项字典 {配置项: 值}
    """
    config = configparser.ConfigParser()
    # 保留原文件大小写（默认转换为小写）
    config.optionxform = lambda option: option
    file_path = get_real_path_create_dir(file_path,path_is_dir=False)
    # 如果文件存在则读取
    if os.path.exists(file_path):
        config.read(file_path, encoding='utf-8')

    # 如果节点不存在则创建
    if not config.has_section(section):
        config.add_section(section)

    # 更新/添加所有配置项
    for key, value in config_dict.items():
        config.set(section, key, str(value))

    # 写入文件（自动创建新文件）
    with open(file_path, 'w', encoding='utf-8') as configfile:
        config.write(configfile) # type: ignore


def list_alive_threads():
    """
    列出当前程序中所有存活的线程。
    返回：线程名列表
    """
    threads = threading.enumerate()
    thread_names = [thread.name for thread in threads]
    return thread_names

def list_child_processes():
    """
    列出当前程序中所有存活的子进程。
    返回：子进程信息列表，每项为dict，包含pid, name等信息
    """
    parent = psutil.Process(os.getpid())
    children = parent.children(recursive=True)
    child_info = []
    for child in children:
        try:
            info = {
                'pid': child.pid,
                'name': child.name(),
                'status': child.status(),
                'create_time': child.create_time()
            }
            child_info.append(info)
        except psutil.NoSuchProcess:
            # 进程可能已经结束，忽略
            pass
    return child_info

def format_time_interval(start_timestamp):
    """
    计算当前时间与输入时间戳格式化为 "xxx天xx:xx:xx"

    参数:
        start_timestamp (float): 输入时间戳（time.time()格式） 起始时间

    返回:
        str: 格式化的时间间隔字符串
    """
    # 1. 获取当前时间戳
    current_timestamp = time.time()

    # 2. 计算时间差（秒）
    time_diff = abs(current_timestamp - start_timestamp)

    # 3. 分解时间差为天、小时、分钟、秒
    days = int(time_diff // (24 * 3600))
    remaining_seconds = time_diff % (24 * 3600)

    hours = int(remaining_seconds // 3600)
    remaining_seconds %= 3600

    minutes = int(remaining_seconds // 60)
    seconds = int(remaining_seconds % 60)

    # 4. 格式化为字符串 (xx:xx:xx 固定2位)
    return time_diff,f"{days}天{hours:02d}:{minutes:02d}:{seconds:02d}"
