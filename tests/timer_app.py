# gui计时器

import tkinter as tk
import time

class BasicTimerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("基本计时器")
        self.root.geometry("300x200")
        self.root.resizable(False, False)

        # 计时器状态
        self.running = False
        self.start_time = 0
        self.elapsed_time = 0

        # 创建界面
        self.create_widgets()

        # 初始化显示
        self.update_display()

    def create_widgets(self):
        # 计时显示
        self.time_label = tk.Label(
            self.root,
            text="00:00:00.000",
            font=("Arial", 28),
            fg="#333",
            bg="#f0f0f0",
            padx=20,
            pady=10
        )
        self.time_label.pack(pady=(20, 10))

        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)

        # 开始/暂停按钮
        self.start_button = tk.Button(
            button_frame,
            text="开始",
            width=8,
            command=self.toggle_timer,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold")
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        # 重置按钮
        reset_button = tk.Button(
            button_frame,
            text="重置",
            width=8,
            command=self.reset_timer,
            bg="#F44336",
            fg="white",
            font=("Arial", 10, "bold")
        )
        reset_button.pack(side=tk.LEFT, padx=5)

        # 退出按钮
        exit_button = tk.Button(
            self.root,
            text="退出",
            width=8,
            command=self.root.quit,
            bg="#607D8B",
            fg="white",
            font=("Arial", 10, "bold")
        )
        exit_button.pack(pady=(10, 20))

    def toggle_timer(self):
        """切换计时器的开始/暂停状态"""
        if not self.running:
            # 开始计时
            self.running = True
            self.start_button.config(text="暂停", bg="#FF9800")
            if self.elapsed_time == 0:
                self.start_time = time.monotonic()
            else:
                # 恢复计时时调整起始时间
                self.start_time = time.monotonic() - self.elapsed_time
            self.update_timer()
        else:
            # 暂停计时
            self.running = False
            self.start_button.config(text="继续", bg="#4CAF50")

    def reset_timer(self):
        """重置计时器"""
        self.running = False
        self.start_button.config(text="开始", bg="#4CAF50")
        self.elapsed_time = 0
        self.update_display()

    def update_timer(self):
        """更新计时器显示"""
        if self.running:
            # 计算经过的时间
            current_time = time.monotonic()
            self.elapsed_time = current_time - self.start_time
            self.update_display()
            # 每10毫秒更新一次显示，实现流畅更新
            self.root.after(10, self.update_timer)

    def update_display(self):
        """格式化并更新时间显示"""
        # 计算小时、分钟、秒和毫秒
        hours = int(self.elapsed_time // 3600)
        minutes = int((self.elapsed_time % 3600) // 60)
        seconds = int(self.elapsed_time % 60)
        milliseconds = int((self.elapsed_time - int(self.elapsed_time)) * 1000)

        # 格式化时间字符串
        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"
        self.time_label.config(text=time_str)


if __name__ == "__main__":
    root = tk.Tk()
    app = BasicTimerApp(root)
    root.mainloop()
